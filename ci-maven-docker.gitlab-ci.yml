include:
  - local: ci-maven.gitlab-ci.yml
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/normalize-image@main
    inputs:
      image-tag: $MAVEN_VERSION
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/docker@main
    inputs:
      image_name: $DOCKER_IMAGE
      image_tag: $DOCKER_TAG
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/publish-charts@main
    inputs:
      application-version: $MAVEN_VERSION
