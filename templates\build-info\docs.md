# Build info

Generates build name and build info and exposes them as environment variables which are available in the following jobs.

The `BUILD_NAME` is constructed by extracting the project namespace from the `CI_PROJECT_NAMESPACE` variable,
removing any leading directory names (if any), and appending the `CI_PROJECT_NAME`.

The `BUILD_NUMBER` is generated by getting the current date and time in the format `YYYYMMDDHHMMSS`.
