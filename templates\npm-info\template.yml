spec:
  inputs:
    stage:
      default: .pre
    image:
      default: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/nodejs:22-latest
---

npm-info:
  stage: $[[ inputs.stage ]]
  image: $[[ inputs.image ]]
  script:
    - NPM_VERSION=$(node -p "require('./package.json').version") ;
    - NPM_NAME=$(node -p "require('./package.json').name") ;
    - echo "NPM_VERSION=$NPM_VERSION" > info.env
    - echo "NPM_NAME=$NPM_NAME" >> info.env
    - cat info.env
  artifacts:
    reports:
      dotenv: info.env