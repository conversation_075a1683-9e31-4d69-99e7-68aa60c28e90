spec:
  inputs:
    test-stage:
      default: test
    cypress-image-name:
      default: docker-all.dach041.dachser.com/cypress/included:14.2.1      

---

cypress-test:
  stage: $[[ inputs.test-stage ]]
  image: 
    name: $[[ inputs.cypress-image-name ]]
    entrypoint: [""]
  script:
    - echo "Running cypress tests..."
    - npm install
    # <PERSON><PERSON><PERSON> proposed stick to login.feature for now, as he guess the other tests will still be unstable
    - npx cypress run --spec cypress/e2e/login.feature
  environment:
    name: ENV_DEV
  allow_failure: false
  when: manual
  artifacts:
    paths:
      - test-results.xml
    reports:
      junit: test-results.xml
