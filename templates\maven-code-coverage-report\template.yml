spec:
  inputs:
    coverage-stage:
      default: post-test
      description: "The stage in which the coverage job will run"
    report-job-name:
      default: "maven-build"
      description: "Name of the test job that generates coverage data"
    jacoco-report-path:
      default: "target/site/jacoco/index.html"
      description: "Path to the JaCoCo index.html file containing coverage data"
    jacoco-xml-path:
      default: "target/site/jacoco/jacoco.xml"
      description: "Path to the JaCoCo XML file for conversion to Cobertura format"
    source-path:
      default: "src/main/java"
      description: "Path to the source code for coverage mapping"
    artifacts-expiry:
      default: "1 week"
      description: "How long to keep the coverage artifacts"
    maven-image-name:
      default: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/maven:jdk-21-v1.1.0
      description: "Maven image to use for coverage reporting"
    visualize-stage:
      default: post-test
      description: "The stage in which the visualization job will run"
    coverage-job-name:
      default: "coverage-report"
      description: "Name of the coverage job that generates JaCoCo reports"
    code-quality-stage:
      default: post-test
      description: "The stage in which the code quality job will run"
    formatter-image-name:
      default: bint-docker-dfe.docker-artifactory.dach041.dachser.com/ci/code-coverage-converter:1.0.0 
      description: "Image to use for code quality formatting"

---

coverage-report:
  stage: $[[ inputs.coverage-stage ]]
  image: $[[ inputs.maven-image-name ]]
  script:
    - |
      COVERAGE_FILE="target/site/jacoco/jacoco.xml"
      if [ ! -f "$COVERAGE_FILE" ]; then
        echo "Warning: JaCoCo XML file not found at $COVERAGE_FILE"
        echo "No JaCoCo coverage reports were generated - skipping coverage processing"
        echo "Total Code Coverage: 0%"
        # Create a simple text report instead
        echo "No coverage data available" > coverage-report.txt
        exit 0
      else
        echo "JaCoCo XML file found, processing coverage data..."
        
        # Look for the first LINE counter (usually the bundle/report level)
        awk '
          BEGIN {
            GREEN = "\033[1;32m"
            YELLOW = "\033[1;33m"
            RED = "\033[1;31m"
            BLUE = "\033[1;34m"
            RESET = "\033[0m"
            found = 0
          }
          /counter type="LINE"/ && !found {
            if (match($0, /missed="([0-9]+)".*covered="([0-9]+)"/, arr)) {
              missed = arr[1]
              covered = arr[2]
              total = missed + covered
              if (total > 0) {
                percentage = (covered / total) * 100
                if (percentage >= 80) {
                  status = GREEN "✅" RESET
                } else if (percentage >= 60) {
                  status = YELLOW "⚠️" RESET
                } else {
                  status = RED "❌" RESET
                }
                printf "Total Code Coverage: " BLUE "%.2f%%" RESET " - %s\n", percentage, status
              } else {
                print "Total Code Coverage: 0%"
              }
              found = 1
            }
          }
          END {
            if (!found) {
              print "Total Code Coverage: 0% - Could not find any LINE counter"
            }
          }
        ' "$COVERAGE_FILE"
        
        # Ensure coverage-report.txt exists to avoid warning message
        if [ ! -f "coverage-report.txt" ]; then
          echo "Coverage processing completed" > coverage-report.txt
        fi
      fi          
  needs: 
    - job: "$[[ inputs.report-job-name ]]"
      optional: true
  rules:
    - if: '$CI_COMMIT_TAG'
      when: never
    - when: always
  artifacts:
    paths:
      - target/site/jacoco/
      - coverage-report.txt
    expire_in: $[[ inputs.artifacts-expiry ]]
    when: always
  coverage: '/Total Code Coverage: ([0-9]{1,3})%/'
  allow_failure: true

# Visualization job that converts JaCoCo XML to Cobertura format for GitLab UI display
coverage-visualize:
  stage: $[[ inputs.coverage-stage ]]
  image: docker-all.dach041.dachser.com/haynes/jacoco2cobertura:1.0.10
  script:
    - |
      mkdir -p target/site
      if [ -f "$[[ inputs.jacoco-xml-path ]]" ]; then
        python /opt/cover2cover.py $[[ inputs.jacoco-xml-path ]] $[[ inputs.source-path ]] > target/site/cobertura.xml
      else
        echo "JaCoCo XML file not found at $[[ inputs.jacoco-xml-path ]]"
        echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?><coverage></coverage>" > target/site/cobertura.xml
      fi
  needs:
    - job: coverage-report
      optional: true
  rules:
    - if: '$CI_COMMIT_TAG'
      when: never
    - when: always
  artifacts:
    paths:
      - target/site/cobertura.xml
    reports:
      coverage_report:
        coverage_format: cobertura
        path: target/site/cobertura.xml
    expire_in: $[[ inputs.artifacts-expiry ]]

# Code quality job that formats coverage data for GitLab UI display
code-quality:
  stage: $[[ inputs.code-quality-stage ]]
  image: $[[ inputs.formatter-image-name ]]
  script:
    - cd $CI_PROJECT_DIR
    - python /app/convert_reports.py
    - ls -la gl-code-quality*.json || echo "No JSON files found"
  artifacts:
    reports:
      codequality: gl-code-quality.json
    paths:
      - gl-code-quality*.json
  rules:
    - if: '$CI_COMMIT_TAG'
      when: never
    - when: always
  allow_failure: true
