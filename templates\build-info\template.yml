spec:
  inputs:
    stage-info:
      default: .pre
    stage-publish-info:
      default: .post
    image:
      default: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/ssh:latest
---
include:
  - local: "/templates/.jfrog.gitlab-ci.yml"

build-info:
  stage: $[[ inputs.stage-info ]]
  image: $[[ inputs.image ]]
  script:
    - BUILD_NAME="${CI_PROJECT_NAMESPACE#*/}__$CI_PROJECT_NAME"
    - BUILD_NUMBER="$(date "+%Y%m%d%H%M%S")"
    - echo "BUILD_NAME=$BUILD_NAME" > info.env
    - echo "BUILD_NUMBER=$BUILD_NUMBER" >> info.env
    - cat info.env
  artifacts:
    reports:
      dotenv: info.env

publish-build-info:
  stage: $[[ inputs.stage-publish-info ]]
  image: $[[ inputs.image ]]
  rules:
    - if: "$CI_COMMIT_TAG"
  script:
    - !reference [.setup_jfrog, script]
    - jf rt build-publish --build-url=$CI_PIPELINE_URL --project=$JFROG_PROJECT $BUILD_NAME $BUILD_NUMBER | tee output.txt
    - jf rt build-discard --max-builds=30 --project=$JFROG_PROJECT --async $BUILD_NAME
