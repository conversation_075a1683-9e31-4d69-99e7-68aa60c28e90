# Maven Code Coverage Report

A GitLab CI template that processes JaCoCo code coverage reports from Java Maven projects.

## Overview

This template adds three jobs to your pipeline:

- **coverage-report**: Extracts coverage metrics from JaCoCo XML reports
- **coverage-visualize**: Converts JaCoCo to Cobertura format for GitLab UI integration
- **code-quality**: Formats coverage data for GitLab code quality display

## Usage

Add to your `.gitlab-ci.yml`:

```yaml
include:
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/templates/maven-code-coverage-report
```

## Configuration

| Parameter | Default | Description |
|-----------|---------|-------------|
| coverage-stage | post-test | Stage for coverage job |
| report-job-name | maven-build | Job that generates coverage data |
| jacoco-xml-path | target/site/jacoco/jacoco.xml | Path to JaCoCo XML file |
| source-path | src/main/java | Source code path for coverage mapping |
| artifacts-expiry | 1 week | Artifact retention period |
| maven-image-name | registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/maven:jdk-21-v1.1.0 | Maven image |
| formatter-image-name | bint-docker-dfe.docker-artifactory.dach041.dachser.com/ci/code-coverage-converter:1.0.0 | Code quality formatter image |

## Requirements

- JaCoCo XML reports must be generated during test execution
- Test job must save JaCoCo reports as artifacts