spec:
  inputs:
    stage:
      default: build
    image-tag:
    image:
      default: docker-all.dach041.dachser.com/debian:12-slim
---
normalize-image-name:
  stage: $[[ inputs.stage ]]
  rules:
    - if: $CI_COMMIT_TAG
  image: $[[ inputs.image ]]
  variables:
    IMAGE_TAG: $[[ inputs.image-tag ]]
  script:
    - |
      IMAGE_NAME=$(echo "$CI_PROJECT_NAME" | tr '[:upper:]' '[:lower:]')

      DOCKER_IMAGE="${IMAGE_TARGET_REPO}.${BASE_DOCKER_REPO}/${IMAGE_GROUP}/${IMAGE_NAME}"

      DOCKER_TAG="${IMAGE_TAG//\//-}"
      DOCKER_TAG=$(echo "$DOCKER_TAG" | sed -r 's/([A-Z])/-\L\1/g' | sed 's/^-//')

      echo "DOCKER_IMAGE=$DOCKER_IMAGE" > info.env
      echo "DOCKER_TAG=$DOCKER_TAG" >> info.env
      cat info.env
  artifacts:
    reports:
      dotenv: info.env
