include:
  - local: ci-base.gitlab-ci.yml
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-info@feature/DFE6WM-294_npm-blueprint
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-build@feature/DFE6WM-294_npm-blueprint
    inputs:
      build-number: $BUILD_NUMBER
      build-name: $BUILD_NAME
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-test@feature/DFE6WM-294_npm_browser
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-code-analysis@feature/DFE6WM-294_npm-blueprint
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-code-coverage-report@feature/DFE6WM-294_npm_browser
    inputs:
      enable-pages: "true"      
  # - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/sonar-scan@main
  #   inputs:
  #     sonar-project-name: $NPM_NAME
  #     sonar-project-version: $NPM_VERSION
    