stages:
  - prepare
  - build
  - test
  - post-test
  - build-images
  - deploy
  - release

default:
  tags:
    - openshift


# Specifies, if a pipeline is created at all. Current configuration runs pipelines for tags, merge requests and
# for branches that are not associated with a merge request.
workflow:
  rules:
    # After release pipeline is finished, the next commit on the same branch will not trigger a new pipeline
    - if: $CI_COMMIT_BRANCH && $CI_COMMIT_MESSAGE =~ /^release:.*/
      when: never
    - if: $CI_COMMIT_TAG && $CI_COMMIT_TAG =~ $ALPHA_PATTERN
      variables:
        ARTIFACTORY_REPO_DEPLOY_RELEASES: $ARTIFACTORY_REPO_DEPLOY_RELEASES_DEV
        ARTIFACTORY_REPO_DEPLOY_SNAPSHOTS: $ARTIFACTORY_REPO_DEPLOY_RELEASES_DEV
        ARTIFACTORY_REPO_DEPLOY_NPM: $ARTIFACTORY_REPO_DEPLOY_NPM_DEV
        NPM_RUN_TESTS: "false"
    - if: $CI_COMMIT_TAG && $CI_COMMIT_TAG =~ $BETA_PATTERN
      variables:
        ARTIFACTORY_REPO_DEPLOY_RELEASES: $ARTIFACTORY_REPO_DEPLOY_RELEASES_BETA
        ARTIFACTORY_REPO_DEPLOY_SNAPSHOTS: $ARTIFACTORY_REPO_DEPLOY_RELEASES_BETA
        ARTIFACTORY_REPO_DEPLOY_NPM: $ARTIFACTORY_REPO_DEPLOY_NPM_BETA
        NPM_RUN_TESTS: "false"
    - if: $CI_COMMIT_TAG && $CI_COMMIT_TAG !~ $BETA_PATTERN && $CI_COMMIT_TAG !~ $ALPHA_PATTERN
      variables:
        ARTIFACTORY_REPO_DEPLOY_RELEASES: $ARTIFACTORY_REPO_DEPLOY_RELEASES_MASTER
        ARTIFACTORY_REPO_DEPLOY_SNAPSHOTS: $ARTIFACTORY_REPO_DEPLOY_RELEASES_MASTER
        ARTIFACTORY_REPO_DEPLOY_NPM: $ARTIFACTORY_REPO_DEPLOY_NPM_MASTER
        NPM_RUN_TESTS: "false"
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH
    - if: $CI_COMMIT_TAG

variables:
  # Branches
  MASTER_BRANCH: "master"
  BETA_BRANCH: "develop"
  ALPHA_BRANCH: "alpha"
  BETA_PATTERN: '/rc/'
  ALPHA_PATTERN: '/alpha/'
  BETA_RELEASES_ACTIVE: "true"

  # Base images
  BUILDER_SSH: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/ssh:latest
  MAVEN_OPENJDK_SLIM: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/maven:jdk-21-v1.1.0
  NODE_SLIM: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/nodejs:22-latest

  # Artifactory
  JFROG_PROJECT: bint
  ARTIFACTORY_REPO_RESOLVE_RELEASES: bint-maven-dfe-all
  ARTIFACTORY_REPO_RESOLVE_SNAPSHOTS: bint-maven-dfe-all
  ARTIFACTORY_REPO_RESOLVE_NPM: npm-all

  ARTIFACTORY_REPO_DEPLOY_RELEASES_DEV: bint-maven-dfe
  ARTIFACTORY_REPO_DEPLOY_SNAPSHOTS_DEV: bint-maven-dfe
  ARTIFACTORY_REPO_DEPLOY_RELEASES_BETA: bint-maven-dfe
  ARTIFACTORY_REPO_DEPLOY_SNAPSHOTS_BETA: bint-maven-dfe
  ARTIFACTORY_REPO_DEPLOY_RELEASES_MASTER: bint-maven-dfe
  ARTIFACTORY_REPO_DEPLOY_SNAPSHOTS_MASTER: bint-maven-dfe

  ARTIFACTORY_REPO_DEPLOY_NPM_DEV: bint-npm-dfe
  ARTIFACTORY_REPO_DEPLOY_NPM_BETA: bint-npm-dfe
  ARTIFACTORY_REPO_DEPLOY_NPM_MASTER: bint-npm-dfe

  # These variables are set in workflow for three main branches, these are defatuls for feature branches and MRs
  ARTIFACTORY_REPO_DEPLOY_RELEASES: $ARTIFACTORY_REPO_DEPLOY_RELEASES_DEV
  ARTIFACTORY_REPO_DEPLOY_SNAPSHOTS: $ARTIFACTORY_REPO_DEPLOY_RELEASES_DEV
  ARTIFACTORY_REPO_DEPLOY_NPM: $ARTIFACTORY_REPO_DEPLOY_NPM_DEV

  # Docker
  BASE_DOCKER_REPO: "docker-artifactory.dach041.dachser.com"
  IMAGE_TARGET_REPO: "bint-docker-dfe"
  IMAGE_GROUP: "dfe"

  NPM_RUN_TESTS: "true"

include:
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/build-info@main
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/semantic-release@main
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/create-mr@main
    